# 🔑 API Setup Instructions

## Getting Your OpenRouter API Key

1. **Visit OpenRouter**: Go to [https://openrouter.ai/](https://openrouter.ai/)

2. **Sign Up/Login**: Create an account or log in to your existing account

3. **Get API Key**: 
   - Navigate to your dashboard
   - Find the API Keys section
   - Generate a new API key
   - Copy the key (it will look like: `sk-or-v1-...`)

## Configuring the App

1. **Open the config file**: Navigate to `config/api.ts` in your project

2. **Replace the placeholder**: 
   ```typescript
   // Change this line:
   OPENROUTER_API_KEY: 'YOUR_OPENROUTER_API_KEY_HERE',
   
   // To this (with your actual key):
   OPENROUTER_API_KEY: 'sk-or-v1-your-actual-key-here',
   ```

3. **Save the file**: The app will automatically reload with the new configuration

## Testing the Setup

1. **Start the app**: Run `npx expo start`
2. **Take a photo**: Use the camera or select an image
3. **Analyze**: Tap "Analyze Plant" 
4. **Check results**: You should see detailed plant information

## Troubleshooting

- **"Please configure your OpenRouter API key"**: The API key is still set to the placeholder
- **"API request failed: 401"**: Invalid API key
- **"API request failed: 429"**: Rate limit exceeded (wait a moment and try again)
- **"Failed to convert image to base64"**: Image processing error (try a different image)

## API Costs

- OpenRouter charges per token used
- Google Gemini 2.5 Flash is very cost-effective
- Image analysis typically costs a few cents per request
- Check your usage on the OpenRouter dashboard

## Security Note

- Never commit your API key to version control
- Consider using environment variables for production apps
- Keep your API key secure and don't share it publicly
